import * as fs from "fs";
import * as path from "path";
import * as csv from "fast-csv";
import { prisma } from "prisma-db";
import { ImageBucket } from "ui/src/utils";
import dotenv from "dotenv";

dotenv.config();
const { S3_BUCKET_NAME, AWS_REGION, SPACE_ID, file } = process.env;

if (!S3_BUCKET_NAME || !AWS_REGION || !SPACE_ID || !file) {
  throw new Error("Missing environment variables");
}

interface FileColumns {
  id: string;
  createdAt: string;
  title: string;
  imageUrl: string;
  isPremium: string;
  isPublished: string;
  articleDate: string;
  language: string;
  seoDescription: string;
  slug: string;
  content: string;
}

const downloadAndUploadFile = async (url: string) => {
  const res = await fetch(url);
  const fileName = url.split("/").pop()!;
  const blob = await res.blob();
  const file = new File([blob], fileName, { type: blob.type });

  const bucket = new ImageBucket();

  await bucket.uploadFile({ key: fileName, file, resize: true });

  return `https://${S3_BUCKET_NAME}.s3.${AWS_REGION}.amazonaws.com/${fileName}`;
};

const writeRecord = async (record: FileColumns) => {
  const imageUrl = await downloadAndUploadFile(record.imageUrl);

  const res = await prisma.article.create({
    data: {
      spaceId: SPACE_ID!,
      articleDate: new Date(record.articleDate),
      createdAt: new Date(record.createdAt),
      isPremium: record.isPremium === "TRUE",
      isPublished: record.isPublished === "FALSE",
      slug: record.slug,
      contents: {
        create: {
          title: record.title,
          imageUrl: imageUrl,
          seoDescription: record.seoDescription,
          content: record.content,
          language: record.language,
        },
      },
    },
  });

  console.log(res);
};

const csvData: FileColumns[] = [];

fs.createReadStream(path.resolve(__dirname, file))
  .pipe(csv.parse({ headers: true }))
  .on("error", (error) => console.error(error))
  .on("data", (row: FileColumns) => {
    csvData.push(row);
  })
  .on("end", async (rowCount: number) => {
    console.log(`Parsed ${rowCount} rows`);

    for (let i = 0; i < csvData.length; i++) {
      const record = csvData[i];
      await writeRecord(record);
    }
  });
