import { prisma } from "prisma-db"
import dotenv from "dotenv";
dotenv.config();

async function main() {
  const list = await prisma.articleContent.findMany();

  for (const entry of list) {
    const newImageUrl = entry.imageUrl.replace(
      "https://shoshin-space.s3.ap-southeast-1.amazonaws.com",
      "https://shoshin-space.s3.ap-southeast-1.amazonaws.com/blog-image",
    );

    await prisma.articleContent.update({
      where: { id: entry.id },
      data: { imageUrl: newImageUrl },
    });
  }
}
main();
