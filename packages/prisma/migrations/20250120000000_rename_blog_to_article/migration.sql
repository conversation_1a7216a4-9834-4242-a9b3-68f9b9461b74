-- Rename Blog table to Article
ALTER TABLE "Blog" RENAME TO "Article";

-- Rename BlogContent table to ArticleContent  
ALTER TABLE "BlogContent" RENAME TO "ArticleContent";

-- Rename blogId column to articleId in ArticleContent table
ALTER TABLE "ArticleContent" RENAME COLUMN "blogId" TO "articleId";

-- Update foreign key constraint names
ALTER TABLE "ArticleContent" DROP CONSTRAINT "BlogContent_blogId_fkey";
ALTER TABLE "ArticleContent" ADD CONSTRAINT "ArticleContent_articleId_fkey" FOREIGN KEY ("articleId") REFERENCES "Article"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Update foreign key constraint for Article table
ALTER TABLE "Article" DROP CONSTRAINT "Blog_spaceId_fkey";
ALTER TABLE "Article" ADD CONSTRAINT "Article_spaceId_fkey" FOREIGN KEY ("spaceId") REFERENCES "Space"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Update many-to-many relationship table for categories
-- First, we need to check if there's a junction table for Blog-Category relationship
-- The junction table is typically named "_BlogToCategory" or similar
-- We'll rename it to "_ArticleToCategory"

-- Note: Prisma automatically creates junction tables with specific naming conventions
-- The actual table name might be different, so this might need adjustment based on the actual schema
ALTER TABLE "_BlogToCategory" RENAME TO "_ArticleToCategory";

-- Update the column names in the junction table if they exist
-- These column names are typically "A" and "B" in Prisma's auto-generated junction tables
-- But if they have specific names, we'd need to update them
-- ALTER TABLE "_ArticleToCategory" RENAME COLUMN "blogId" TO "articleId";
