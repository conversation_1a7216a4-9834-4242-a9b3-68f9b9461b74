/* eslint-disable @next/next/no-img-element */
import { redirect } from "next/navigation";
import { ArticleContent } from "./components/article-content";
import { formatArticlePath, formatDate } from "@/app/utils";
import {
  queryArticleById,
  queryArticleBySlug,
  queryArticleListForStaticParams,
} from "@/app/api/read/article/query";

const PROD_URL = process.env.SITE_URL;

export async function generateStaticParams() {
  const articles = await queryArticleListForStaticParams();

  const paramList: { slug: string; lang: string }[] = [];

  articles.forEach(({ id, slug, contents }) => {
    contents.forEach(({ language }) => {
      paramList.push({
        slug: slug || id,
        lang: language === "en" ? "" : language,
      });
    });
  });

  return paramList;
}

interface ArticlePageQuery {
  params: Promise<{ slug: string; lang: string }>;
}

export async function generateMetadata({ params }: ArticlePageQuery) {
  const { slug, lang } = await params;
  const data =
    (await queryArticleBySlug(slug, lang)) ||
    (await queryArticleById(slug, lang));

  if (!data) {
    return {};
  }

  const { title, seoDescription, id, Article } = data;

  return {
    title: title + " | " + Article?.Space.title,
    description: seoDescription,
    openGraph: {
      title: title,
      url: PROD_URL + formatArticlePath({ slug, lang, id }),
      siteName: Article?.Space.title,
      images: data?.imageUrl,
      type: "website",
    },
  };
}

export default async function ArticlePage({ params }: ArticlePageQuery) {
  const { slug, lang } = await params;
  const data =
    (await queryArticleBySlug(slug, lang)) ||
    (await queryArticleById(slug, lang));

  if (!data?.id) {
    return redirect("/");
  }

  const { title, content, imageUrl, Article } = data;

  const resizedImageUrl = imageUrl.replace(
    "/blog-image/",
    "/blog-image-resize-800/",
  );

  return (
    <div className="w-full max-w-5xl mx-auto p-5 md:p-10 space-y-10">
      <div className="sm:px-10 space-y-5">
        <h1 className="text-3xl font-bold">{title}</h1>
        <p className="text-sm text-gray-400">
          {formatDate(Article?.articleDate)}
        </p>
      </div>

      {imageUrl ? (
        <div className="w-full aspect-21/9 relative">
          <picture>
            <source srcSet={`${resizedImageUrl}`} media="(max-width: 768px)" />
            <source srcSet={`${imageUrl}`} />
            <img
              alt="banner cover"
              fetchPriority="high"
              loading="eager"
              className="w-full h-full rounded-md border object-center object-cover"
              src={imageUrl}
            />
          </picture>
        </div>
      ) : (
        <hr />
      )}

      <ArticleContent content={content} isPremium={Article?.isPremium} />
    </div>
  );
}
