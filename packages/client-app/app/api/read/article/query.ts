import { prisma } from "prisma-db";

export const queryArticleBySlug = (slug: string, lang: string | null) =>
  prisma.articleContent.findFirst({
    where: {
      language: lang || "en",
      Article: { slug, spaceId: process.env.SPACE_ID },
    },
    include: {
      Article: {
        include: {
          Space: true,
        },
      },
    },
  });

export const queryArticleById = (id: string, lang: string | null) =>
  prisma.articleContent.findFirst({
    where: {
      language: lang || "en",
      Article: { spaceId: process.env.SPACE_ID },
      id,
    },
    include: {
      Article: {
        include: {
          Space: true,
        },
      },
    },
  });

export const queryArticleListForStaticParams = () =>
  prisma.article.findMany({
    where: { isPublished: true },
    include: {
      contents: {
        where: { isReady: true },
      },
    },
  });
