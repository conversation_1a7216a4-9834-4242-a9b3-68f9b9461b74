import { prisma } from "prisma-db";

const spaceId = process.env.SPACE_ID ?? "";

export const queryArticleList = (
  lang: string | undefined | null,
  category?: string | undefined | null,
) => {
  console.log({ lang, category });
  return prisma.articleContent.findMany({
    where: {
      language: lang || "en",
      isReady: true,
      Article: {
        spaceId,
        isPublished: true,
        categories: category ? { some: { id: category } } : undefined,
      },
    },
    include: { Article: true },
    orderBy: { Article: { articleDate: "desc" } },
  });
};

export type ArticleListResponse = Awaited<ReturnType<typeof queryArticleList>>;
