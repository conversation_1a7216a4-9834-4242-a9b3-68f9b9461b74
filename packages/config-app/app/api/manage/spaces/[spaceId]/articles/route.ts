import { auth } from "@/auth";
import { prisma } from "prisma-db";
import { z } from "zod";

const createArticleSchema = z.object({
  article: z.object({
    articleDate: z.string().transform((str) => new Date(str)),
    isPremium: z.boolean(),
    isPublished: z.boolean(),
    slug: z.string(),
    tags: z.array(z.string()).optional().default([]),
    categories: z.array(z.string()).optional().default([]),
  }),
  content: z.object({
    title: z.string().min(1, "Title is required"),
    imageUrl: z.string().optional().default(""),
    seoDescription: z.string().optional().default(""),
    content: z.string().optional().default(""),
    language: z.enum(["en", "ja", "zh"]),
  }),
});

export type CreateArticleInput = z.infer<typeof createArticleSchema>;

// GET - List articles for a specific space (admin only)
export async function GET(
  req: Request,
  { params }: { params: Promise<{ spaceId: string }> },
) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  const { spaceId } = await params;

  try {
    const articles = await prisma.article.findMany({
      where: { spaceId: spaceId },
      include: {
        contents: { orderBy: { language: "asc" } },
        categories: {
          include: {
            labels: true,
          },
        },
      },
      orderBy: { articleDate: "desc" },
    });

    return Response.json(articles);
  } catch (error) {
    return Response.json(
      {
        message: "Failed to fetch articles",
        error,
      },
      { status: 500 },
    );
  }
}

// POST - Create new article in a specific space (admin only)
export async function POST(
  req: Request,
  { params }: { params: Promise<{ spaceId: string }> },
) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  const { spaceId } = await params;

  try {
    const data = await req.json();
    const validatedData = createArticleSchema.parse(data);

    // Create the article
    const article = await prisma.article.create({
      data: {
        ...validatedData.article,
        spaceId: spaceId,
        categories: validatedData.article.categories?.length
          ? {
              connect: validatedData.article.categories.map((id) => ({ id })),
            }
          : undefined,
      },
    });

    // Create the content
    const content = await prisma.articleContent.create({
      data: {
        ...validatedData.content,
        articleId: article.id,
      },
    });

    return Response.json({ article, content });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        {
          message: "Invalid article data",
          errors: error.errors,
        },
        { status: 400 },
      );
    }

    return Response.json(
      {
        message: "Failed to create article",
        error,
      },
      { status: 500 },
    );
  }
}
