import { auth } from "@/auth";
import { prisma } from "prisma-db";
import { z } from "zod";

const updateArticleSchema = z.object({
  article: z
    .object({
      articleDate: z
        .string()
        .transform((str) => new Date(str))
        .optional(),
      isPremium: z.boolean().optional(),
      isPublished: z.boolean().optional(),
      slug: z.string().optional(),
      tags: z.array(z.string()).optional(),
      categories: z.array(z.string()).optional(),
    })
    .optional(),
  content: z
    .object({
      title: z.string().min(1, "Title is required").optional(),
      imageUrl: z.string().optional(),
      seoDescription: z.string().optional(),
      content: z.string().optional(),
      language: z.enum(["en", "ja", "zh"]).optional(),
    })
    .optional(),
});

export type UpdateArticleInput = z.infer<typeof updateArticleSchema>;

// GET - Get single article (admin only)
export async function GET(
  req: Request,
  { params }: { params: Promise<{ spaceId: string; articleId: string }> },
) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  const { spaceId, articleId } = await params;

  try {
    const article = await prisma.article.findFirst({
      where: {
        id: articleId,
        spaceId: spaceId,
      },
      include: {
        contents: { orderBy: { language: "asc" } },
        categories: {
          include: {
            labels: true,
          },
        },
      },
    });

    if (!article) {
      return Response.json({ message: "Article not found" }, { status: 404 });
    }

    return Response.json(article);
  } catch (error) {
    return Response.json(
      {
        message: "Failed to fetch article",
        error,
      },
      { status: 500 },
    );
  }
}

// PUT - Update article (admin only)
export async function PUT(
  req: Request,
  { params }: { params: Promise<{ spaceId: string; articleId: string }> },
) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  const { spaceId, articleId } = await params;

  try {
    const data = await req.json();
    const validatedData = updateArticleSchema.parse(data);

    // Check if article exists and belongs to the space
    const existingArticle = await prisma.article.findFirst({
      where: {
        id: articleId,
        spaceId: spaceId,
      },
    });

    if (!existingArticle) {
      return Response.json({ message: "Article not found" }, { status: 404 });
    }

    // Update article if article data is provided
    let updatedArticle = existingArticle;
    if (validatedData.article) {
      const { categories, ...articleData } = validatedData.article;

      updatedArticle = await prisma.article.update({
        where: { id: articleId },
        data: {
          ...articleData,
          categories: categories
            ? {
                set: categories.map((id) => ({ id })),
              }
            : undefined,
        },
      });
    }

    // Update content if content data is provided
    let updatedContent = null;
    if (validatedData.content) {
      const language = validatedData.content.language || "en";

      // Try to find existing content for this article and language
      const existingContent = await prisma.articleContent.findFirst({
        where: {
          articleId: articleId,
          language,
        },
      });

      if (existingContent) {
        // Update existing content
        updatedContent = await prisma.articleContent.update({
          where: { id: existingContent.id },
          data: validatedData.content,
        });
      } else {
        // Create new content with required fields
        updatedContent = await prisma.articleContent.create({
          data: {
            title: validatedData.content.title || "",
            imageUrl: validatedData.content.imageUrl || "",
            seoDescription: validatedData.content.seoDescription || "",
            content: validatedData.content.content || "",
            language,
            articleId: articleId,
          },
        });
      }
    }

    return Response.json({ article: updatedArticle, content: updatedContent });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        {
          message: "Invalid article data",
          errors: error.errors,
        },
        { status: 400 },
      );
    }

    return Response.json(
      {
        message: "Failed to update article",
        error,
      },
      { status: 500 },
    );
  }
}

// DELETE - Delete article (admin only)
export async function DELETE(
  _req: Request,
  { params }: { params: Promise<{ spaceId: string; articleId: string }> },
) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  const { spaceId, articleId } = await params;

  try {
    // Check if article exists and belongs to the space
    const existingArticle = await prisma.article.findFirst({
      where: {
        id: articleId,
        spaceId: spaceId,
      },
    });

    if (!existingArticle) {
      return Response.json({ message: "Article not found" }, { status: 404 });
    }

    // Delete the article (contents will be deleted due to cascade)
    await prisma.article.delete({
      where: { id: articleId },
    });

    return Response.json({ message: "Article deleted successfully" });
  } catch (error) {
    return Response.json(
      {
        message: "Failed to delete article",
        error,
      },
      { status: 500 },
    );
  }
}
