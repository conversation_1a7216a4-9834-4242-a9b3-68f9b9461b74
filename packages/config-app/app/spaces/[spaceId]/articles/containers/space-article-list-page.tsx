"use client";
import React, { useState } from "react";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import Link from "next/link";
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Trash2, ArrowLeft, Edit, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { SpaceRenderTitle } from "../components/space-render-title";
import { SpaceTranslateButton } from "../components/space-translate-button";

interface ArticleContent {
  id: string;
  title: string;
  language: string;
  imageUrl: string;
  seoDescription: string;
  content: string;
  isReady: boolean;
}

interface Article {
  id: string;
  slug: string;
  articleDate: string;
  updatedAt: string;
  isPremium: boolean;
  isPublished: boolean;
  tags: string[];
  contents: ArticleContent[];
  categories: Array<{
    id: string;
    labels: Array<{
      id: string;
      name: string;
      language: string;
    }>;
  }>;
}

const fetchSpaceArticles = async (spaceId: string): Promise<Article[]> => {
  const response = await fetch(`/api/manage/spaces/${spaceId}/articles`);
  if (!response.ok) {
    throw new Error("Failed to fetch articles");
  }
  return response.json();
};

const columnHelper = createColumnHelper<Article>();

export function SpaceArticleListPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const params = useParams();
  const spaceId = params.spaceId as string;
  const [operationId, setOperationId] = useState<string | null>(null);

  const { data: articles, isPending } = useQuery({
    queryKey: ["space-articles", spaceId],
    queryFn: () => fetchSpaceArticles(spaceId),
    enabled: !!spaceId,
  });

  const { data: space } = useQuery({
    queryKey: ["space", spaceId],
    queryFn: async () => {
      const response = await fetch(`/api/manage/spaces/${spaceId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch space");
      }
      return response.json();
    },
    enabled: !!spaceId,
  });

  const deleteArticleMutation = useMutation({
    mutationFn: async (articleId: string) => {
      const response = await fetch(
        `/api/manage/spaces/${spaceId}/articles/${articleId}`,
        {
          method: "DELETE",
        },
      );
      if (!response.ok) {
        throw new Error("Failed to delete article");
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Article deleted successfully",
      });
      queryClient.invalidateQueries({ queryKey: ["space-articles", spaceId] });
      setOperationId(null);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete article",
        variant: "destructive",
      });
      console.error("Delete error:", error);
    },
  });

  const columns = [
    columnHelper.accessor("contents", {
      header: "Title",
      cell: ({ row }) => (
        <SpaceRenderTitle record={row.original} spaceId={spaceId} />
      ),
    }),
    columnHelper.accessor("isPremium", {
      header: "Premium",
      cell: ({ getValue }) => (getValue() ? "Premium" : ""),
      size: 100,
    }),
    columnHelper.accessor("isPublished", {
      header: "Published",
      cell: ({ getValue }) => (
        <span
          className={`inline-flex items-center ${getValue() ? "text-green-600" : "text-gray-500"}`}
        >
          {getValue() ? "● Published" : "○ Draft"}
        </span>
      ),
      size: 120,
    }),
    columnHelper.accessor("articleDate", {
      header: "Article Date",
      cell: ({ getValue }) => {
        const date = getValue();
        return new Date(date).toLocaleDateString("en-US", {
          year: "numeric",
          month: "short",
          day: "numeric",
        });
      },
      size: 120,
    }),
    columnHelper.accessor("updatedAt", {
      header: "Last Updated",
      cell: ({ getValue }) => {
        const date = getValue();
        return new Date(date).toLocaleDateString("en-US", {
          year: "2-digit",
          month: "2-digit",
          day: "2-digit",
          hour: "numeric",
          minute: "numeric",
          hour12: false,
        });
      },
      size: 150,
    }),
    columnHelper.accessor("id", {
      header: "Actions",
      cell: ({ getValue, row }) => (
        <div className="flex items-center justify-end space-x-2">
          <SpaceTranslateButton
            record={row.original}
            onSuccess={revalidateList}
          />
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/spaces/${spaceId}/articles/${row.original.id}/edit`}>
              <Edit className="h-4 w-4" />
            </Link>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setOperationId(getValue());
            }}
          >
            <Trash2 className="h-4 w-4 text-red-500" />
          </Button>
        </div>
      ),
      size: 120,
    }),
  ];

  const revalidateList = () =>
    queryClient.invalidateQueries({
      queryKey: ["space-articles", spaceId],
      exact: true,
    });

  const table = useReactTable({
    data: articles || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const handleDelete = () => {
    if (operationId) {
      deleteArticleMutation.mutate(operationId);
    }
  };

  if (isPending) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-lg">Loading articles...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/spaces/${spaceId}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Space
          </Link>
        </Button>
      </div>

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            Articles
          </h1>
          <p className="text-muted-foreground">
            {`Manage articles for "${space?.title}"`}
          </p>
        </div>
        <Button asChild>
          <Link href={`/spaces/${spaceId}/articles/new`}>
            <Plus className="h-4 w-4 mr-2" />
            Create Article
          </Link>
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    style={{ width: header.getSize() }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <Dialog open={!!operationId} onOpenChange={() => setOperationId(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Article</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this article? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOperationId(null)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleteArticleMutation.isPending}
            >
              {deleteArticleMutation.isPending ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
