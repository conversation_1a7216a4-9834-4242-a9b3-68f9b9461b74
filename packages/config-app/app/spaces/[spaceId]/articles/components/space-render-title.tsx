import { Edit } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Toolt<PERSON>,
  TooltipContent,
  Too<PERSON>ipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useRouter } from "next/navigation";

interface ArticleContent {
  id: string;
  title: string;
  language: string;
  imageUrl: string;
  seoDescription: string;
  content: string;
  isReady: boolean;
}

interface Article {
  id: string;
  slug: string;
  articleDate: string;
  isPremium: boolean;
  isPublished: boolean;
  tags: string[];
  contents: ArticleContent[];
  categories: Array<{
    id: string;
    labels: Array<{
      id: string;
      name: string;
      language: string;
    }>;
  }>;
}

export const SpaceRenderTitle = ({
  record,
  spaceId,
}: {
  record: Article;
  spaceId: string;
}) => {
  const router = useRouter();
  const { contents } = record;

  return (
    <TooltipProvider>
      <div className="flex items-center gap-2 relative">
        <Avatar className="shrink-0 rounded-md">
          <AvatarImage src={contents[0]?.imageUrl || undefined} />
          <AvatarFallback className="rounded-md">IMG</AvatarFallback>
        </Avatar>
        <div>
          {contents.map((entry) => {
            const { title, language, isReady } = entry;
            const inCompleteFields = Object.entries({
              ...entry,
              ...record,
            }).filter(([, value]) => value === "");
            return (
              <div
                key={language}
                className="flex gap-1 group cursor-pointer"
                onClick={() =>
                  router.push(
                    `/spaces/${spaceId}/articles/${record.id}/edit?lang=${language}`,
                  )
                }
              >
                <div className={"line-clamp-1"}>
                  <span>[{language}] </span>
                  {!isReady && (
                    <span className="text-yellow-500">[draft] </span>
                  )}{" "}
                  {title}
                </div>
                {!!inCompleteFields.length && (
                  <Tooltip>
                    <TooltipTrigger>
                      <span>⚠️</span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <span>
                        Empty fields:{" "}
                        <b>{inCompleteFields.map(([key]) => key).join(", ")}</b>
                      </span>
                    </TooltipContent>
                  </Tooltip>
                )}
                <Edit className="h-4 w-4 invisible group-hover:visible" />
              </div>
            );
          })}
        </div>
      </div>
    </TooltipProvider>
  );
};
